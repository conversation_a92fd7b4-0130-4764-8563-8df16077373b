"use client"

import React from "react"
import { useAuth } from "@/lib/auth-provider"
import { useLocations } from "@/lib/location-provider"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface LocationButtonsProps {
  className?: string
  onLocationChange?: (locationId: string) => void
}

export function LocationButtons({ className, onLocationChange }: LocationButtonsProps) {
  const { currentLocation, setCurrentLocation } = useAuth()
  const { getLocationById, getActiveLocations, isHomeServiceEnabled } = useLocations()

  // Handle location button click
  const handleLocationClick = (locationId: string) => {
    console.log(`🔘 Location button clicked: ${locationId}`)
    
    // Update global location state
    setCurrentLocation(locationId)
    
    // Call optional callback
    if (onLocationChange) {
      onLocationChange(locationId)
    }
    
    console.log(`🔘 Global location state updated to: ${locationId}`)
  }

  // Get all active locations and create location buttons dynamically
  const allActiveLocations = getActiveLocations()



  // Separate regular locations from special locations (same logic as LocationSelector)
  const regularLocations = allActiveLocations.filter(loc =>
    loc.id !== "home" && loc.id !== "online"
  );
  const onlineLocation = allActiveLocations.find(loc => loc.id === "online");
  const homeLocation = allActiveLocations.find(loc => loc.id === "home");

  // Define the location buttons to match LocationSelector structure exactly
  const locationButtons = [
    { id: "all", label: "All" },
    // Add Online Store option (matching LocationSelector)
    ...(onlineLocation ? [{ id: "online", label: "Online Store" }] : []),
    // Add regular locations in the same order as they appear in the database
    ...regularLocations.map(location => ({
      id: location.id,
      label: location.name
    })),
    // Add Home Service option if enabled (matching LocationSelector)
    ...(isHomeServiceEnabled && homeLocation ? [{ id: "home", label: "Home service" }] : [])
  ]



  return (
    <div className={cn("flex flex-wrap", className)}>
      {locationButtons.map((location, index) => {
        const isFirst = index === 0
        const isLast = index === locationButtons.length - 1
        const isSelected = currentLocation === location.id

        return (
          <Button
            key={location.id}
            variant={isSelected ? "default" : "outline"}
            className={cn(
              "border-r-0 last:border-r",
              {
                "rounded-l-md rounded-r-none": isFirst,
                "rounded-none": !isFirst && !isLast,
                "rounded-r-md rounded-l-none": isLast,
                "bg-black text-white hover:bg-gray-800": isSelected,
              }
            )}
            onClick={() => handleLocationClick(location.id)}
          >
            {location.label}
          </Button>
        )
      })}
    </div>
  )
}
