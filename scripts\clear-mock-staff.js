#!/usr/bin/env node

/**
 * Clear Mock Staff Data Script
 * 
 * This script clears localStorage staff data to ensure only real database staff is loaded
 */

console.log('🧹 Clearing mock staff data from localStorage...');

// This script is meant to be run in the browser console
// Since we can't directly access localStorage from Node.js, we'll create a client-side script

const clearMockStaffScript = `
// Clear localStorage staff data
console.log('🧹 Clearing localStorage staff data...');
localStorage.removeItem('vanity_staff');
console.log('✅ Cleared vanity_staff from localStorage');

// Clear any other staff-related cache
const keysToRemove = [];
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  if (key && key.includes('staff')) {
    keysToRemove.push(key);
  }
}

keysToRemove.forEach(key => {
  localStorage.removeItem(key);
  console.log('✅ Cleared', key, 'from localStorage');
});

console.log('🎉 All mock staff data cleared! Refresh the page to load only real database staff.');
`;

console.log('📋 Copy and paste the following script into your browser console:');
console.log('');
console.log(clearMockStaffScript);
console.log('');
console.log('Or visit the appointments page and the system will automatically filter out mock staff.');
